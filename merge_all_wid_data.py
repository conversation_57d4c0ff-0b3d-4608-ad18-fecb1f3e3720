 #!/usr/bin/env python3
"""
合并所有WID下载的Excel文件
将580个文件按列排列，文件间用空列分隔
"""

import pandas as pd
import numpy as np
from pathlib import Path
import re
import json
from datetime import datetime


def load_wid_progress():
    """加载WID进度信息"""
    progress_file = Path("flame_wid_downloads/wid_progress.json")
    
    if not progress_file.exists():
        print("❌ 找不到进度文件")
        return []
    
    with open(progress_file, 'r', encoding='utf-8') as f:
        progress_data = json.load(f)
    
    completed_wids = progress_data.get('completed_wids', [])
    print(f"📊 发现 {len(completed_wids)} 个已完成的WID")
    
    return sorted(completed_wids)


def get_excel_files():
    """获取所有Excel文件"""
    raw_files_dir = Path("flame_wid_downloads/raw_files")
    
    if not raw_files_dir.exists():
        print("❌ 找不到raw_files目录")
        return []
    
    excel_files = list(raw_files_dir.glob("*.xls"))
    print(f"📁 发现 {len(excel_files)} 个Excel文件")
    
    return sorted(excel_files)


def extract_wid_from_filename(filename):
    """从文件名提取WID"""
    # 文件名格式: 100-M20-Ne-Neon.xls
    match = re.match(r'^(\d+)-', filename.name)
    if match:
        return int(match.group(1))
    return None


def read_excel_data(file_path):
    """读取Excel文件数据"""
    try:
        # 尝试读取Excel文件
        df = pd.read_excel(file_path, engine='xlrd')
        
        # 如果数据为空，返回空DataFrame
        if df.empty:
            return pd.DataFrame()
        
        # 清理数据：移除完全空的行和列
        df = df.dropna(how='all').dropna(axis=1, how='all')
        
        return df
    
    except Exception as e:
        print(f"  ❌ 读取文件失败: {e}")
        return pd.DataFrame()


def merge_all_wid_files():
    """合并所有WID文件"""
    print("🔄 开始合并所有WID数据文件")
    print("=" * 60)
    
    # 获取文件列表
    excel_files = get_excel_files()
    completed_wids = load_wid_progress()
    
    if not excel_files:
        print("❌ 没有找到Excel文件")
        return
    
    # 按WID排序文件
    wid_files = []
    for file_path in excel_files:
        wid = extract_wid_from_filename(file_path)
        if wid and wid in completed_wids:
            wid_files.append((wid, file_path))
    
    wid_files.sort(key=lambda x: x[0])  # 按WID排序
    
    print(f"📋 准备合并 {len(wid_files)} 个有效文件")
    
    # 数据存储
    all_data = []
    max_rows = 0
    file_info = []
    
    # 读取所有文件数据
    print("\n📖 读取文件数据...")
    for i, (wid, file_path) in enumerate(wid_files, 1):
        print(f"  [{i}/{len(wid_files)}] 读取WID {wid}: {file_path.name}")
        
        df = read_excel_data(file_path)
        
        if df.empty:
            print(f"    ⚠️ 文件为空，跳过")
            continue
        
        # 记录文件信息
        file_info.append({
            'WID': wid,
            'FileName': file_path.name,
            'Rows': len(df),
            'Cols': len(df.columns),
            'StartCol': 0,  # 稍后计算
            'EndCol': 0     # 稍后计算
        })
        
        all_data.append(df)
        max_rows = max(max_rows, len(df))
        
        if i % 50 == 0:
            print(f"    📈 已处理 {i} 个文件...")
    
    if not all_data:
        print("❌ 没有有效数据")
        return
    
    print(f"\n✅ 成功读取 {len(all_data)} 个文件")
    print(f"📏 最大行数: {max_rows}")
    
    # 计算总列数（包括分隔列）
    total_cols = 0
    for df in all_data:
        total_cols += len(df.columns) + 2  # 每个文件后加2个空列作分隔
    
    print(f"📐 预计总列数: {total_cols}")
    
    # 创建合并后的DataFrame
    print("\n🔗 开始列排列合并...")
    
    # 创建空的结果DataFrame
    merged_data = pd.DataFrame(index=range(max_rows))
    current_col = 0
    
    # 逐个文件添加到结果中
    for i, (df, info) in enumerate(zip(all_data, file_info)):
        # 记录起始列
        info['StartCol'] = current_col + 1  # Excel列从1开始
        
        # 添加数据列
        for col_idx, col_name in enumerate(df.columns):
            # 扩展数据到最大行数
            col_data = df[col_name].reindex(range(max_rows))
            merged_data[f'Col_{current_col}'] = col_data
            current_col += 1
        
        # 记录结束列
        info['EndCol'] = current_col
        
        # 添加2个空列作为分隔（除了最后一个文件）
        if i < len(all_data) - 1:
            merged_data[f'Sep1_{current_col}'] = np.nan
            merged_data[f'Sep2_{current_col+1}'] = np.nan
            current_col += 2
        
        # 进度显示
        if (i + 1) % 20 == 0:
            print(f"    📊 已合并 {i+1}/{len(all_data)} 个文件...")
    
    print(f"🎯 合并完成！最终尺寸: {len(merged_data)} 行 x {len(merged_data.columns)} 列")
    
    # 保存结果
    output_file = "FLAME_WID_Complete_Database.xlsx"
    print(f"\n💾 保存到文件: {output_file}")
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主数据表
        merged_data.to_excel(writer, sheet_name='Complete_Database', index=False, header=False)
        
        # 文件索引表
        file_index_df = pd.DataFrame(file_info)
        file_index_df.to_excel(writer, sheet_name='File_Index', index=False)
        
        # 统计信息表
        stats_data = {
            'Statistics': [
                'Total Files',
                'Total Rows', 
                'Total Columns',
                'Max File Rows',
                'Min File Rows',
                'Avg File Rows',
                'Generation Time'
            ],
            'Value': [
                len(all_data),
                len(merged_data),
                len(merged_data.columns),
                max(info['Rows'] for info in file_info),
                min(info['Rows'] for info in file_info),
                round(sum(info['Rows'] for info in file_info) / len(file_info), 1),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ]
        }
        stats_df = pd.DataFrame(stats_data)
        stats_df.to_excel(writer, sheet_name='Statistics', index=False)
        
        # WID范围表
        wid_ranges = []
        for i in range(0, len(file_info), 50):  # 每50个WID一组
            batch = file_info[i:i+50]
            wid_ranges.append({
                'Batch': f"{i//50 + 1}",
                'WID_Range': f"{batch[0]['WID']}-{batch[-1]['WID']}",
                'Files_Count': len(batch),
                'Col_Range': f"{batch[0]['StartCol']}-{batch[-1]['EndCol']}"
            })
        
        wid_ranges_df = pd.DataFrame(wid_ranges)
        wid_ranges_df.to_excel(writer, sheet_name='WID_Ranges', index=False)
    
    print(f"✅ 文件保存成功!")
    print(f"\n📋 包含工作表:")
    print(f"  • Complete_Database: 主数据 ({len(merged_data)} x {len(merged_data.columns)})")
    print(f"  • File_Index: 文件索引 ({len(file_info)} 个文件)")
    print(f"  • Statistics: 统计信息")
    print(f"  • WID_Ranges: WID范围分组")
    
    return output_file


def main():
    """主函数"""
    print("🗂️ FLAME WID数据库合并器")
    print("=" * 60)
    print("将所有580个WID文件按列排列，文件间用空列分隔")
    print("=" * 60)
    
    # 检查依赖
    try:
        import openpyxl
    except ImportError:
        print("❌ 缺少openpyxl库，正在安装...")
        import subprocess
        subprocess.run(["pip", "install", "openpyxl"])
        import openpyxl
    
    # 执行合并
    result_file = merge_all_wid_files()
    
    if result_file:
        print(f"\n🎉 合并完成！")
        print(f"📄 输出文件: {result_file}")
        print(f"🔍 请检查文件内容确认合并效果")
    else:
        print(f"\n❌ 合并失败")


if __name__ == "__main__":
    main()