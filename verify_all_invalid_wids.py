#!/usr/bin/env python3
"""
重新验证所有无效WID
对progress.json中标记为invalid的117个WID进行重新验证
"""

import asyncio
import json
from pathlib import Path
from flame_wid_explorer import Flame<PERSON><PERSON>xplorer
from playwright.async_api import async_playwright


def get_all_invalid_wids():
    """获取所有标记为无效的WID"""
    print("📊 读取无效WID列表")
    print("=" * 50)
    
    # 读取进度文件
    progress_file = Path("flame_wid_downloads/wid_progress.json")
    
    if not progress_file.exists():
        print("❌ 找不到进度文件")
        return []
    
    with open(progress_file, 'r', encoding='utf-8') as f:
        progress_data = json.load(f)
    
    invalid_wids = progress_data.get('invalid_wids', [])
    completed_wids = progress_data.get('completed_wids', [])
    failed_wids = progress_data.get('failed_wids', [])
    
    print(f"🚫 标记为无效的WID: {len(invalid_wids)} 个")
    print(f"✅ 已成功下载: {len(completed_wids)} 个")
    print(f"❌ 下载失败: {len(failed_wids)} 个")
    
    print(f"\n📋 无效WID列表:")
    print(f"  {sorted(invalid_wids)}")
    
    return sorted(invalid_wids)


async def verify_invalid_wids(invalid_wids):
    """重新验证所有无效WID"""
    if not invalid_wids:
        print("✅ 没有需要验证的无效WID")
        return
    
    print(f"\n🔍 开始重新验证所有 {len(invalid_wids)} 个无效WID")
    print("=" * 60)
    
    # 创建探索器
    explorer = FlameWIDExplorer(
        base_dir="flame_wid_downloads", 
        delay=1.0,
        headless=True,
        max_retries=3
    )
    
    results = {
        'still_invalid': [],
        'now_valid': [],
        'download_success': [],
        'download_failed': []
    }
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            for i, wid in enumerate(invalid_wids, 1):
                print(f"\n🔍 [{i}/{len(invalid_wids)}] 验证WID {wid}:")
                
                try:
                    # 测试WID是否存在
                    exists, info = await explorer.test_wid_exists(page, wid)
                    
                    if not exists:
                        print(f"  🚫 确认仍然无效")
                        results['still_invalid'].append(wid)
                    else:
                        print(f"  ✅ 发现这个WID实际有效!")
                        print(f"    质量: {info.get('mass', 'N/A')}")
                        print(f"    分子式: {info.get('formula', 'N/A')}")
                        print(f"    物种: {info.get('species', 'N/A')}")
                        
                        results['now_valid'].append(wid)
                        
                        # 尝试下载
                        print(f"  📥 尝试下载...")
                        download_success = await explorer.download_wid_data(page, wid, info)
                        
                        if download_success:
                            print(f"  🎉 下载成功!")
                            results['download_success'].append(wid)
                            
                            # 更新进度文件
                            try:
                                # 先从invalid_wids中移除
                                if wid in explorer.progress_manager.progress_data['invalid_wids']:
                                    explorer.progress_manager.progress_data['invalid_wids'].remove(wid)
                                # 标记为成功
                                explorer.progress_manager.mark_wid_success(wid, info)
                            except PermissionError:
                                print(f"    ⚠️ 权限问题，无法更新进度文件，但文件已下载")
                        else:
                            print(f"  ❌ 下载失败")
                            results['download_failed'].append(wid)
                            try:
                                # 从invalid移到failed
                                if wid in explorer.progress_manager.progress_data['invalid_wids']:
                                    explorer.progress_manager.progress_data['invalid_wids'].remove(wid)
                                explorer.progress_manager.mark_wid_failed(wid, "验证有效但下载失败")
                            except PermissionError:
                                pass
                
                except Exception as e:
                    print(f"  ❌ 验证出错: {e}")
                
                # 每10个WID显示一次进度
                if i % 10 == 0:
                    print(f"\n📈 进度统计 ({i}/{len(invalid_wids)}):")
                    print(f"  仍然无效: {len(results['still_invalid'])}")
                    print(f"  发现有效: {len(results['now_valid'])}")
                    print(f"  下载成功: {len(results['download_success'])}")
                
                # 适当延迟
                await asyncio.sleep(0.5)
        
        finally:
            await browser.close()
    
    # 最终结果报告
    print(f"\n🎯 验证完成！最终统计:")
    print("=" * 60)
    print(f"📊 总验证数量: {len(invalid_wids)}")
    print(f"🚫 确认仍然无效: {len(results['still_invalid'])} 个")
    print(f"✅ 发现实际有效: {len(results['now_valid'])} 个")
    print(f"🎉 成功下载: {len(results['download_success'])} 个")
    print(f"❌ 下载失败: {len(results['download_failed'])} 个")
    
    if results['now_valid']:
        print(f"\n🔥 意外发现的有效WID:")
        print(f"  {results['now_valid']}")
    
    if results['download_success']:
        print(f"\n💾 新增下载的文件:")
        for wid in results['download_success']:
            print(f"  WID {wid}")
    
    return results


async def main():
    """主函数"""
    print("🔍 FLAME WID无效数据重新验证器")
    print("=" * 50)
    print("重新验证所有117个标记为'无效'的WID")
    print("有些WID可能因为网络问题被误判为无效")
    print("=" * 50)
    
    # 获取所有无效WID
    invalid_wids = get_all_invalid_wids()
    
    if not invalid_wids:
        print("✅ 没有需要验证的无效WID")
        return
    
    # 询问用户是否继续
    user_input = input(f"\n是否要重新验证所有 {len(invalid_wids)} 个无效WID？(y/n): ").strip().lower()
    
    if user_input != 'y':
        print("⏭️ 取消验证")
        return
    
    # 开始验证
    results = await verify_invalid_wids(invalid_wids)
    
    print(f"\n🎊 验证完成！")
    print(f"如果有新下载的文件，请查看 flame_wid_downloads/raw_files 目录")


if __name__ == "__main__":
    asyncio.run(main()) 