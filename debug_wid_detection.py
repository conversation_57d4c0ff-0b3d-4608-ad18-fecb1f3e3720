#!/usr/bin/env python3
"""
调试WID检测问题
专门检查WID 653为什么被误判为无效
"""

import asyncio
from playwright.async_api import async_playwright


async def debug_wid_653():
    """详细调试WID 653"""
    print("🔍 调试WID 653检测问题")
    print("=" * 60)
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # 可视化模式便于调试
        page = await browser.new_page()
        
        try:
            wid = 653
            url = f"http://flame.nsrl.ustc.edu.cn/database/data.php?wid={wid}"
            print(f"📎 测试URL: {url}")
            
            # 访问页面
            print("⏳ 正在加载页面...")
            await page.goto(url)
            await page.wait_for_load_state('networkidle')
            await page.wait_for_timeout(2000)  # 等待2秒
            
            # 1. 检查页面基本信息
            print("\n📋 页面基本信息:")
            title = await page.title()
            print(f"  标题: {title}")
            
            # 2. 检查页面内容长度
            page_text = await page.text_content('body')
            print(f"  页面内容长度: {len(page_text) if page_text else 0} 字符")
            
            if page_text:
                print(f"  前200字符: {page_text[:200]}...")
                print(f"  包含'下载数据': {'下载数据' in page_text}")
                print(f"  包含'查看数据': {'查看数据' in page_text}")
                print(f"  包含'.xls': {'.xls' in page_text}")
            
            # 3. 检查所有链接
            print("\n🔗 页面所有链接:")
            all_links = await page.locator('a').all()
            for i, link in enumerate(all_links):
                try:
                    href = await link.get_attribute('href')
                    text = await link.text_content()
                    print(f"  链接{i+1}: '{text}' -> {href}")
                except:
                    pass
            
            # 4. 具体检查下载链接
            print("\n📥 下载链接检测:")
            
            # 方法1: 查找"下载数据"文本
            download_text_links = await page.locator('a:has-text("下载数据")').count()
            print(f"  'a:has-text(\"下载数据\")': {download_text_links} 个")
            
            # 方法2: 查找包含.xls的链接
            xls_links = await page.locator('a[href*=".xls"]').count()
            print(f"  'a[href*=\".xls\"]': {xls_links} 个")
            
            # 方法3: 更宽泛的查找
            download_links = await page.locator('a:text-matches(".*下载.*", "i")').count()
            print(f"  包含'下载'的链接: {download_links} 个")
            
            # 方法4: 查找所有可能的下载相关链接
            possible_downloads = await page.locator('a').filter(has_text="下载").count()
            print(f"  filter(has_text='下载'): {possible_downloads} 个")
            
            # 5. 检查表单元素
            print("\n📊 表单元素:")
            inputs = await page.locator('input').count()
            selects = await page.locator('select').count()
            print(f"  input元素: {inputs} 个")
            print(f"  select元素: {selects} 个")
            
            # 6. 检查质量输入框
            mass_inputs = await page.locator('input[type="text"]').all()
            for i, input_elem in enumerate(mass_inputs):
                try:
                    value = await input_elem.input_value()
                    print(f"  输入框{i+1} 值: '{value}'")
                except:
                    pass
            
            # 7. 检查选择框
            select_elements = await page.locator('select').all()
            for i, select_elem in enumerate(select_elements):
                try:
                    selected_option = select_elem.locator('option[selected]')
                    if await selected_option.count() > 0:
                        text = await selected_option.text_content()
                        print(f"  选择框{i+1} 选中值: '{text}'")
                except:
                    pass
            
            # 8. 尝试实际检测逻辑
            print("\n🧪 使用原始检测逻辑:")
            
            # 模拟原始的test_wid_exists逻辑
            if not page_text or len(page_text.strip()) < 100:
                print("  ❌ 页面内容检查: 失败 (内容过少)")
            else:
                print("  ✅ 页面内容检查: 通过")
            
            download_link = page.locator('a:has-text("下载数据")')
            download_count = await download_link.count()
            
            if download_count == 0:
                alt_download = page.locator('a[href*=".xls"]')
                alt_count = await alt_download.count()
                if alt_count == 0:
                    print("  ❌ 下载链接检查: 失败 (未找到下载链接)")
                else:
                    print(f"  ✅ 下载链接检查: 通过 (找到{alt_count}个.xls链接)")
            else:
                print(f"  ✅ 下载链接检查: 通过 (找到{download_count}个'下载数据'链接)")
            
            print(f"\n🔚 调试完成，按回车键关闭浏览器...")
            input()
            
        except Exception as e:
            print(f"❌ 调试过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()


async def test_multiple_wids():
    """测试多个已知的WID"""
    print("\n" + "="*60)
    print("🧪 测试多个已知WID")
    print("="*60)
    
    # 从之前测试中知道有效的WID
    known_good_wids = [118, 125, 213, 214, 100, 200, 300, 500, 653]
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)  # 无头模式快速测试
        page = await browser.new_page()
        
        try:
            for wid in known_good_wids:
                print(f"\n🔍 测试WID {wid}:")
                url = f"http://flame.nsrl.ustc.edu.cn/database/data.php?wid={wid}"
                
                try:
                    await page.goto(url)
                    await page.wait_for_load_state('networkidle')
                    await page.wait_for_timeout(1000)
                    
                    # 页面内容检查
                    page_text = await page.text_content('body')
                    content_check = page_text and len(page_text.strip()) >= 100
                    
                    # 下载链接检查
                    download_link = page.locator('a:has-text("下载数据")')
                    download_count = await download_link.count()
                    
                    if download_count == 0:
                        alt_download = page.locator('a[href*=".xls"]')
                        alt_count = await alt_download.count()
                        link_check = alt_count > 0
                    else:
                        link_check = True
                    
                    result = "✅" if (content_check and link_check) else "❌"
                    print(f"  {result} 内容: {content_check} | 链接: {link_check} | 长度: {len(page_text) if page_text else 0}")
                    
                except Exception as e:
                    print(f"  ❌ 错误: {e}")
        
        finally:
            await browser.close()


if __name__ == "__main__":
    # 首先详细调试WID 653
    asyncio.run(debug_wid_653())
    
    # 然后快速测试多个已知WID
    asyncio.run(test_multiple_wids()) 