#!/usr/bin/env python3
"""
FLAME数据库WID智能探索下载器
通过直接遍历WID号来下载所有数据，更高效且简单
作者发现可以通过改变wid来进入不同来源物质的下载页面
"""

import asyncio
import json
import pandas as pd
from pathlib import Path
from datetime import datetime
from playwright.async_api import async_playwright
import logging
from typing import Dict, List, Optional, Tuple
import time


class WIDProgressManager:
    """WID下载进度管理器"""
    
    def __init__(self, progress_file: str = "wid_progress.json"):
        self.progress_file = Path(progress_file)
        self.progress_data = self.load_progress()
    
    def load_progress(self) -> Dict:
        """加载进度数据"""
        if self.progress_file.exists():
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {
            'completed_wids': [],
            'failed_wids': [],
            'invalid_wids': [],
            'last_wid': 0,
            'start_time': datetime.now().isoformat(),
            'last_update': datetime.now().isoformat(),
            'total_downloaded': 0,
            'total_failed': 0,
            'estimated_max_wid': 653
        }
    
    def save_progress(self):
        """保存进度数据"""
        self.progress_data['last_update'] = datetime.now().isoformat()
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(self.progress_data, f, ensure_ascii=False, indent=2)
    
    def mark_wid_success(self, wid: int, file_info: Dict):
        """标记WID下载成功"""
        if wid not in self.progress_data['completed_wids']:
            self.progress_data['completed_wids'].append(wid)
        self.progress_data['total_downloaded'] += 1
        self.progress_data['last_wid'] = wid
        self.save_progress()
    
    def mark_wid_failed(self, wid: int, error: str):
        """标记WID下载失败"""
        if wid not in self.progress_data['failed_wids']:
            self.progress_data['failed_wids'].append(wid)
        self.progress_data['total_failed'] += 1
        self.progress_data['last_wid'] = wid
        self.save_progress()
    
    def mark_wid_invalid(self, wid: int):
        """标记WID无效（不存在）"""
        if wid not in self.progress_data['invalid_wids']:
            self.progress_data['invalid_wids'].append(wid)
        self.progress_data['last_wid'] = wid
        self.save_progress()
    
    def get_remaining_wids(self, start_wid: int = 1, max_wid: int = None) -> List[int]:
        """获取剩余需要处理的WID列表"""
        if max_wid is None:
            max_wid = self.progress_data['estimated_max_wid']
        
        all_wids = set(range(start_wid, max_wid + 1))
        processed_wids = set(self.progress_data['completed_wids'] + 
                           self.progress_data['failed_wids'] + 
                           self.progress_data['invalid_wids'])
        
        return sorted(list(all_wids - processed_wids))
    
    def print_status(self):
        """打印当前状态"""
        total_processed = (len(self.progress_data['completed_wids']) + 
                         len(self.progress_data['failed_wids']) + 
                         len(self.progress_data['invalid_wids']))
        
        print(f"\n📊 当前进度状态:")
        print(f"  ✅ 成功下载: {len(self.progress_data['completed_wids'])} 个WID")
        print(f"  ❌ 下载失败: {len(self.progress_data['failed_wids'])} 个WID")
        print(f"  🚫 无效WID: {len(self.progress_data['invalid_wids'])} 个")
        print(f"  📈 总处理数: {total_processed} 个")
        print(f"  🎯 最后WID: {self.progress_data['last_wid']}")
        print(f"  📁 下载文件: {self.progress_data['total_downloaded']} 个")


class FlameWIDExplorer:
    """FLAME WID智能探索下载器"""
    
    def __init__(self, 
                 base_dir: str = "flame_wid_downloads",
                 delay: float = 1.0,
                 headless: bool = True,
                 max_retries: int = 3):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        self.delay = delay
        self.headless = headless
        self.max_retries = max_retries
        
        # 设置日志
        self.setup_logging()
        
        # WID下载的基础URL
        self.base_url = "http://flame.nsrl.ustc.edu.cn/database/data.php"
        
        # 进度管理器
        self.progress_manager = WIDProgressManager(
            str(self.base_dir / "wid_progress.json")
        )
        
        # 创建下载目录
        self.raw_dir = self.base_dir / "raw_files"
        self.raw_dir.mkdir(exist_ok=True)
        
        # 失败记录
        self.failed_downloads = []
    
    def setup_logging(self):
        """设置日志记录"""
        log_file = self.base_dir / "wid_download.log"
        
        # 创建logger
        self.logger = logging.getLogger('FlameWIDExplorer')
        self.logger.setLevel(logging.INFO)
        
        # 清除已有的handlers
        self.logger.handlers.clear()
        
        # 文件handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    async def test_wid_exists(self, page, wid: int) -> Tuple[bool, Optional[Dict]]:
        """
        测试WID是否存在并获取基本信息
        
        Returns:
            (存在标志, 物质信息字典或None)
        """
        try:
            wid_url = f"{self.base_url}?wid={wid}"
            await page.goto(wid_url)
            await page.wait_for_load_state('networkidle')
            await page.wait_for_timeout(max(1000, int(self.delay * 2000)))  # 至少等待1秒
            
            # 检查是否有错误页面或空页面
            page_text = await page.text_content('body')
            
            if not page_text or len(page_text.strip()) < 100:
                return False, None
            
            # 检查是否有下载链接
            download_link = page.locator('a:has-text("下载数据")')
            if await download_link.count() == 0:
                # 尝试其他下载链接
                alt_download = page.locator('a[href*=".xls"]')
                if await alt_download.count() == 0:
                    return False, None
            
            # 尝试提取物质信息
            info = await self.extract_substance_info(page, wid)
            return True, info
            
        except Exception as e:
            self.logger.warning(f"测试WID {wid} 时出错: {e}")
            return False, None
    
    async def extract_substance_info(self, page, wid: int) -> Dict:
        """从页面提取物质信息"""
        info = {
            'wid': wid,
            'mass': None,
            'formula': None,
            'species': None,
            'source': None
        }
        
        try:
            # 尝试从输入框获取质量
            mass_input = page.locator('input[type="text"]')
            if await mass_input.count() > 0:
                mass_value = await mass_input.input_value()
                if mass_value:
                    try:
                        info['mass'] = int(mass_value)
                    except:
                        pass
            
            # 获取分子式
            formula_select = page.locator('select').first
            if await formula_select.count() > 0:
                selected_option = formula_select.locator('option[selected]')
                if await selected_option.count() > 0:
                    info['formula'] = await selected_option.text_content()
            
            # 获取物种
            species_select = page.locator('select').nth(1)
            if await species_select.count() > 0:
                selected_option = species_select.locator('option[selected]')
                if await selected_option.count() > 0:
                    info['species'] = await selected_option.text_content()
            
            # 获取来源信息
            checked_radio = page.locator('input[type="radio"][checked]')
            if await checked_radio.count() > 0:
                parent = checked_radio.locator('..')
                source_text = await parent.text_content()
                info['source'] = source_text.strip() if source_text else None
            
        except Exception as e:
            self.logger.warning(f"提取WID {wid} 信息时出错: {e}")
        
        return info
    
    async def download_wid_data(self, page, wid: int, info: Dict) -> bool:
        """下载指定WID的数据"""
        try:
            # 查找下载链接
            download_link = page.locator('a:has-text("下载数据")')
            
            if await download_link.count() == 0:
                # 尝试其他下载链接
                download_link = page.locator('a[href*=".xls"]')
                if await download_link.count() == 0:
                    self.logger.warning(f"WID {wid}: 未找到下载链接")
                    return False
            
            # 执行下载
            async with page.expect_download() as download_info:
                await download_link.click()
            download = await download_info.value
            
            # 生成文件名
            filename = self.generate_filename(wid, info)
            filepath = self.raw_dir / filename
            
            # 保存文件
            await download.save_as(filepath)
            
            # 验证文件
            if not filepath.exists() or filepath.stat().st_size == 0:
                self.logger.error(f"WID {wid}: 下载文件为空或不存在")
                return False
            
            # 验证Excel文件
            try:
                df = pd.read_excel(filepath, header=None)
                info.update({
                    'filename': filename,
                    'filepath': str(filepath),
                    'rows': len(df),
                    'cols': len(df.columns),
                    'file_size': filepath.stat().st_size
                })
                
                self.logger.info(f"✅ WID {wid}: 下载成功 - {filename} ({info['rows']}行 x {info['cols']}列)")
                return True
                
            except Exception as e:
                self.logger.error(f"WID {wid}: Excel文件验证失败 - {e}")
                return False
            
        except Exception as e:
            self.logger.error(f"WID {wid}: 下载失败 - {e}")
            return False
    
    def generate_filename(self, wid: int, info: Dict) -> str:
        """生成安全的文件名"""
        parts = [str(wid)]
        
        if info.get('mass'):
            parts.append(f"M{info['mass']}")
        
        if info.get('formula'):
            safe_formula = "".join(c for c in info['formula'] if c.isalnum())
            if safe_formula:
                parts.append(safe_formula)
        
        if info.get('species'):
            safe_species = "".join(c for c in info['species'][:20] if c.isalnum() or c in (' ', '-'))
            safe_species = safe_species.strip().replace(' ', '-')
            if safe_species:
                parts.append(safe_species)
        
        return "-".join(parts) + ".xls"
    
    async def intelligent_wid_exploration(self, start_wid: int = 1, max_wid: int = 700):
        """
        智能WID探索
        动态调整探索范围，智能检测上限
        """
        self.logger.info(f"🚀 开始智能WID探索 (范围: {start_wid}-{max_wid})")
        
        consecutive_failures = 0
        max_consecutive_failures = 50  # 连续失败50个WID后停止
        
        successful_downloads = []
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=self.headless)
            page = await browser.new_page()
            
            try:
                # 获取需要处理的WID列表
                remaining_wids = self.progress_manager.get_remaining_wids(start_wid, max_wid)
                
                if not remaining_wids:
                    self.logger.info("✅ 所有WID已处理完成")
                    return
                
                self.logger.info(f"📝 剩余 {len(remaining_wids)} 个WID需要处理")
                self.progress_manager.print_status()
                
                for i, wid in enumerate(remaining_wids):
                    try:
                        self.logger.info(f"\n{'='*50}")
                        self.logger.info(f"进度: {i+1}/{len(remaining_wids)} - WID {wid}")
                        self.logger.info(f"{'='*50}")
                        
                        # 测试WID是否存在
                        exists, info = await self.test_wid_exists(page, wid)
                        
                        if not exists:
                            self.logger.info(f"❌ WID {wid}: 不存在或无效")
                            self.progress_manager.mark_wid_invalid(wid)
                            consecutive_failures += 1
                            
                            # 如果连续失败太多，可能已经超出范围
                            if consecutive_failures >= max_consecutive_failures:
                                self.logger.info(f"⚠️ 连续{max_consecutive_failures}个WID无效，可能已超出范围")
                                # 更新估计的最大WID
                                self.progress_manager.progress_data['estimated_max_wid'] = wid - max_consecutive_failures
                                self.progress_manager.save_progress()
                                break
                            
                            continue
                        
                        # 重置连续失败计数
                        consecutive_failures = 0
                        
                        # 下载数据
                        success = await self.download_wid_data(page, wid, info)
                        
                        if success:
                            self.progress_manager.mark_wid_success(wid, info)
                            successful_downloads.append(info)
                        else:
                            error_msg = f"下载失败 - WID {wid}"
                            self.progress_manager.mark_wid_failed(wid, error_msg)
                            self.failed_downloads.append({
                                'wid': wid,
                                'error': error_msg,
                                'info': info,
                                'timestamp': datetime.now().isoformat()
                            })
                        
                        # 显示进度
                        if (i + 1) % 10 == 0:
                            completed = len(self.progress_manager.progress_data['completed_wids'])
                            failed = len(self.progress_manager.progress_data['failed_wids'])
                            invalid = len(self.progress_manager.progress_data['invalid_wids'])
                            self.logger.info(f"📈 阶段进度: 成功{completed} | 失败{failed} | 无效{invalid}")
                        
                        # 适当延迟避免过于频繁的请求
                        await asyncio.sleep(self.delay)
                        
                    except KeyboardInterrupt:
                        self.logger.info("⚠️ 用户中断下载")
                        break
                    except Exception as e:
                        self.logger.error(f"处理WID {wid} 时出错: {e}")
                        self.progress_manager.mark_wid_failed(wid, str(e))
                        continue
            
            finally:
                await browser.close()
        
        # 生成探索报告
        self.generate_exploration_report(successful_downloads)
    
    def generate_exploration_report(self, successful_downloads: List[Dict]):
        """生成探索报告"""
        report_file = self.base_dir / "exploration_report.xlsx"
        
        self.logger.info(f"📊 生成探索报告: {report_file}")
        
        try:
            # 成功下载统计
            if successful_downloads:
                df_success = pd.DataFrame(successful_downloads)
                
                # 按质量分组统计
                if 'mass' in df_success.columns and df_success['mass'].notna().any():
                    mass_stats = df_success[df_success['mass'].notna()].groupby('mass').agg({
                        'wid': 'count',
                        'rows': 'sum',
                        'file_size': 'sum'
                    }).rename(columns={'wid': '文件数量', 'rows': '总行数', 'file_size': '总大小'})
                else:
                    mass_stats = pd.DataFrame()
            else:
                df_success = pd.DataFrame()
                mass_stats = pd.DataFrame()
            
            # 失败下载记录
            if self.failed_downloads:
                df_failed = pd.DataFrame(self.failed_downloads)
            else:
                df_failed = pd.DataFrame()
            
            # 总体统计
            total_stats = {
                '成功下载WID数': len(self.progress_manager.progress_data['completed_wids']),
                '失败下载WID数': len(self.progress_manager.progress_data['failed_wids']),
                '无效WID数': len(self.progress_manager.progress_data['invalid_wids']),
                '成功下载文件数': self.progress_manager.progress_data['total_downloaded'],
                '估计最大WID': self.progress_manager.progress_data['estimated_max_wid'],
                '探索开始时间': self.progress_manager.progress_data['start_time'],
                '最后更新时间': self.progress_manager.progress_data['last_update']
            }
            
            # 保存到Excel
            with pd.ExcelWriter(report_file, engine='openpyxl') as writer:
                # 总体统计
                pd.DataFrame([total_stats]).to_excel(writer, sheet_name='总体统计', index=False)
                
                # 成功下载详情
                if not df_success.empty:
                    df_success.to_excel(writer, sheet_name='成功下载', index=False)
                
                # 按质量统计
                if not mass_stats.empty:
                    mass_stats.to_excel(writer, sheet_name='按质量统计')
                
                # 失败记录
                if not df_failed.empty:
                    df_failed.to_excel(writer, sheet_name='失败记录', index=False)
                
                # WID列表
                all_wids_data = []
                for wid in self.progress_manager.progress_data['completed_wids']:
                    all_wids_data.append({'WID': wid, '状态': '成功'})
                for wid in self.progress_manager.progress_data['failed_wids']:
                    all_wids_data.append({'WID': wid, '状态': '失败'})
                for wid in self.progress_manager.progress_data['invalid_wids']:
                    all_wids_data.append({'WID': wid, '状态': '无效'})
                
                if all_wids_data:
                    all_wids = pd.DataFrame(all_wids_data)
                    all_wids.to_excel(writer, sheet_name='WID状态列表', index=False)
            
            self.logger.info(f"✅ 探索报告生成完成")
            
            # 打印最终统计
            print(f"\n🎯 探索完成总结:")
            print(f"  ✅ 成功下载: {total_stats['成功下载WID数']} 个WID")
            print(f"  ❌ 下载失败: {total_stats['失败下载WID数']} 个WID") 
            print(f"  🚫 无效WID: {total_stats['无效WID数']} 个")
            print(f"  📁 下载文件: {total_stats['成功下载文件数']} 个")
            print(f"  📊 详细报告: {report_file}")
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
    
    def generate_retry_list(self) -> List[int]:
        """生成需要重试的WID列表"""
        return self.progress_manager.progress_data['failed_wids'].copy()
    
    async def retry_failed_wids(self):
        """重试失败的WID"""
        failed_wids = self.generate_retry_list()
        
        if not failed_wids:
            self.logger.info("✅ 没有需要重试的WID")
            return
        
        self.logger.info(f"🔄 开始重试 {len(failed_wids)} 个失败的WID")
        
        # 清除失败记录，准备重试
        for wid in failed_wids:
            if wid in self.progress_manager.progress_data['failed_wids']:
                self.progress_manager.progress_data['failed_wids'].remove(wid)
        self.progress_manager.save_progress()
        
        # 重新下载失败的WID
        await self.intelligent_wid_exploration(min(failed_wids), max(failed_wids))


def main():
    """主函数"""
    print("🧪 FLAME WID智能探索下载器")
    print("=" * 50)
    print("通过智能遍历WID来下载所有FLAME数据")
    print("=" * 50)
    
    # 配置参数
    START_WID = 1
    MAX_WID = 700  # 初始估计上限
    BASE_DIR = "flame_wid_downloads"
    HEADLESS = True  # 设为False可以看到浏览器操作
    DELAY = 0.5  # 请求间隔（秒）
    
    try:
        # 创建探索器
        explorer = FlameWIDExplorer(
            base_dir=BASE_DIR,
            delay=DELAY,
            headless=HEADLESS,
            max_retries=3
        )
        
        print(f"🎯 配置:")
        print(f"  WID范围: {START_WID}-{MAX_WID}")
        print(f"  下载目录: {BASE_DIR}")
        print(f"  浏览器模式: {'无头' if HEADLESS else '可视化'}")
        print(f"  请求延迟: {DELAY}秒")
        
        # 询问用户操作
        print(f"\n📋 可选操作:")
        print(f"  1. 智能探索下载 (推荐)")
        print(f"  2. 重试失败的WID")
        print(f"  3. 查看当前状态")
        
        choice = input("\n请选择操作 (1-3): ").strip()
        
        if choice == "1":
            # 开始智能探索
            asyncio.run(explorer.intelligent_wid_exploration(START_WID, MAX_WID))
        elif choice == "2":
            # 重试失败的WID
            asyncio.run(explorer.retry_failed_wids())
        elif choice == "3":
            # 显示状态
            explorer.progress_manager.print_status()
        else:
            print("❌ 无效选择")
            return
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 