#!/usr/bin/env python3
"""
测试WID检测修复效果
快速验证几个已知WID是否能正确检测
"""

import asyncio
from flame_wid_explorer import FlameWIDExplorer


async def test_wid_fix():
    """测试WID检测修复效果"""
    print("🔧 测试WID检测修复效果")
    print("=" * 50)
    
    # 创建探索器
    explorer = FlameWIDExplorer(
        base_dir="test_wid_fix",
        delay=0.5,
        headless=True,
        max_retries=3
    )
    
    # 测试几个已知的有效WID
    test_wids = [118, 125, 213, 214, 653, 100, 200, 300, 500]
    
    from playwright.async_api import async_playwright
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        success_count = 0
        total_count = len(test_wids)
        
        try:
            for wid in test_wids:
                print(f"\n🔍 测试WID {wid}:")
                
                # 使用修复后的检测方法
                exists, info = await explorer.test_wid_exists(page, wid)
                
                if exists:
                    print(f"  ✅ 检测成功!")
                    print(f"    质量: {info.get('mass', 'N/A')}")
                    print(f"    分子式: {info.get('formula', 'N/A')}")
                    print(f"    物种: {info.get('species', 'N/A')}")
                    success_count += 1
                else:
                    print(f"  ❌ 检测失败 (这个WID应该是有效的)")
        
        finally:
            await browser.close()
        
        print(f"\n📊 测试结果总结:")
        print(f"  成功检测: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count == total_count:
            print("  🎉 所有已知有效WID都能正确检测!")
        elif success_count > 0:
            print("  ⚠️ 部分WID检测成功，可能还需要进一步调整")
        else:
            print("  ❌ 检测完全失败，需要进一步调试")


if __name__ == "__main__":
    asyncio.run(test_wid_fix()) 