#!/usr/bin/env python3
"""
简单测试WID访问方法
验证是否可以直接通过WID访问FLAME数据库的不同数据源
"""

import asyncio
from playwright.async_api import async_playwright


async def test_wid_access():
    """测试WID直接访问"""
    print("🧪 测试WID直接访问方法")
    print("=" * 50)
    
    # 已知的一些WID进行测试
    test_wids = [118, 125, 213, 214, 1, 2, 100, 200, 300, 500]
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # 可视化模式
        page = await browser.new_page()
        
        try:
            for wid in test_wids:
                print(f"\n🔍 测试WID: {wid}")
                
                # 构造URL
                url = f"http://flame.nsrl.ustc.edu.cn/database/data.php?wid={wid}"
                print(f"  📎 URL: {url}")
                
                try:
                    # 访问页面
                    await page.goto(url)
                    await page.wait_for_load_state('networkidle')
                    await page.wait_for_timeout(1000)
                    
                    # 检查页面内容
                    page_text = await page.text_content('body')
                    
                    if not page_text or len(page_text.strip()) < 100:
                        print(f"  ❌ WID {wid}: 页面为空或内容过少")
                        continue
                    
                    # 查找下载链接
                    download_links = await page.locator('a:has-text("下载数据")').count()
                    alt_download_links = await page.locator('a[href*=".xls"]').count()
                    
                    if download_links > 0 or alt_download_links > 0:
                        print(f"  ✅ WID {wid}: 找到下载链接 (下载按钮:{download_links} | Excel链接:{alt_download_links})")
                        
                        # 尝试获取物质信息
                        mass_input = page.locator('input[type="text"]')
                        if await mass_input.count() > 0:
                            mass_value = await mass_input.input_value()
                            print(f"    📊 质量: {mass_value}")
                        
                        # 检查选择框
                        selects = await page.locator('select').count()
                        if selects > 0:
                            first_select = page.locator('select').first
                            selected_option = first_select.locator('option[selected]')
                            if await selected_option.count() > 0:
                                formula = await selected_option.text_content()
                                print(f"    🧪 分子式: {formula}")
                        
                        if selects > 1:
                            second_select = page.locator('select').nth(1)
                            selected_option = second_select.locator('option[selected]')
                            if await selected_option.count() > 0:
                                species = await selected_option.text_content()
                                print(f"    🔬 物种: {species}")
                        
                        # 检查来源信息
                        checked_radio = page.locator('input[type="radio"][checked]')
                        if await checked_radio.count() > 0:
                            parent = checked_radio.locator('..')
                            source_text = await parent.text_content()
                            print(f"    📖 来源: {source_text.strip()[:50]}...")
                        
                    else:
                        print(f"  ❌ WID {wid}: 未找到下载链接")
                
                except Exception as e:
                    print(f"  ❌ WID {wid}: 访问出错 - {e}")
                
                # 短暂延迟
                await asyncio.sleep(0.5)
        
        finally:
            print(f"\n🔚 按回车键关闭浏览器...")
            input()
            await browser.close()


if __name__ == "__main__":
    asyncio.run(test_wid_access()) 