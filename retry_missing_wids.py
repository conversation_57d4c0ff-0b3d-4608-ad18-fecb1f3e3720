#!/usr/bin/env python3
"""
重新下载缺失的WID
基于progress.json文件识别1-653范围内缺失的WID并重新下载
"""

import asyncio
import json
from pathlib import Path
from flame_wid_explorer import FlameWIDExplorer
from playwright.async_api import async_playwright


def analyze_missing_wids():
    """分析缺失的WID"""
    print("📊 分析WID下载状况")
    print("=" * 50)
    
    # 读取进度文件
    progress_file = Path("flame_wid_downloads/wid_progress.json")
    
    if not progress_file.exists():
        print("❌ 找不到进度文件")
        return [], [], []
    
    with open(progress_file, 'r', encoding='utf-8') as f:
        progress_data = json.load(f)
    
    completed_wids = set(progress_data.get('completed_wids', []))
    invalid_wids = set(progress_data.get('invalid_wids', []))
    failed_wids = set(progress_data.get('failed_wids', []))
    
    print(f"✅ 成功下载: {len(completed_wids)} 个WID")
    print(f"🚫 无效WID: {len(invalid_wids)} 个")
    print(f"❌ 失败WID: {len(failed_wids)} 个")
    
    # 分析1-653范围内的情况
    target_range = set(range(1, 654))  # 1-653
    all_processed = completed_wids | invalid_wids | failed_wids
    
    # 找出1-653范围内标记为无效但可能需要重试的WID
    invalid_in_range = invalid_wids & target_range
    missing_wids = target_range - all_processed
    retry_invalid = []
    
    # 从无效WID中筛选出一些可能需要重试的
    # 跳过1-67（这些确实无效），重点关注中间的一些
    for wid in invalid_in_range:
        if wid > 67:  # 只重试67以上的WID
            retry_invalid.append(wid)
    
    print(f"\n🔍 1-653范围分析:")
    print(f"  总目标WID: {len(target_range)} 个")
    print(f"  已处理: {len(all_processed & target_range)} 个")
    print(f"  完全缺失: {len(missing_wids)} 个")
    print(f"  标记为无效但需重试: {len(retry_invalid)} 个")
    
    if missing_wids:
        print(f"  缺失WID: {sorted(missing_wids)}")
    
    if retry_invalid:
        print(f"  需重试的'无效'WID: {sorted(retry_invalid)}")
    
    return sorted(missing_wids), sorted(retry_invalid), sorted(failed_wids)


async def retry_specific_wids(wid_list, description):
    """重试特定的WID列表"""
    if not wid_list:
        print(f"✅ {description}: 无需重试")
        return
    
    print(f"\n🔄 开始重试{description}")
    print(f"目标WID: {wid_list}")
    print("=" * 50)
    
    # 创建探索器
    explorer = FlameWIDExplorer(
        base_dir="flame_wid_downloads",
        delay=1.0,  # 稍微增加延迟确保稳定
        headless=True,
        max_retries=3
    )
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        success_count = 0
        
        try:
            for i, wid in enumerate(wid_list, 1):
                print(f"\n🔍 [{i}/{len(wid_list)}] 重试WID {wid}:")
                
                try:
                    # 测试WID是否存在
                    exists, info = await explorer.test_wid_exists(page, wid)
                    
                    if exists:
                        # 尝试下载
                        download_success = await explorer.download_wid_data(page, wid, info)
                        
                        if download_success:
                            print(f"  ✅ 下载成功!")
                            print(f"    质量: {info.get('mass', 'N/A')}")
                            print(f"    分子式: {info.get('formula', 'N/A')}")
                            print(f"    物种: {info.get('species', 'N/A')}")
                            
                            # 手动更新进度（避免权限问题）
                            try:
                                explorer.progress_manager.mark_wid_success(wid, info)
                                success_count += 1
                            except PermissionError:
                                print(f"    ⚠️ 权限问题，无法更新进度文件，但文件已下载")
                                success_count += 1
                        else:
                            print(f"  ❌ 下载失败")
                            try:
                                explorer.progress_manager.mark_wid_failed(wid, "重试下载失败")
                            except PermissionError:
                                pass
                    else:
                        print(f"  🚫 确认无效")
                        try:
                            explorer.progress_manager.mark_wid_invalid(wid)
                        except PermissionError:
                            pass
                
                except Exception as e:
                    print(f"  ❌ 处理出错: {e}")
                
                # 适当延迟
                await asyncio.sleep(1)
        
        finally:
            await browser.close()
        
        print(f"\n📊 {description}结果:")
        print(f"  成功: {success_count}/{len(wid_list)} ({success_count/len(wid_list)*100:.1f}%)")


async def main():
    """主函数"""
    print("🔄 FLAME WID缺失数据重新下载器")
    print("=" * 50)
    
    # 分析缺失的WID
    missing_wids, retry_invalid_wids, failed_wids = analyze_missing_wids()
    
    # 重试失败的WID
    if failed_wids:
        await retry_specific_wids(failed_wids, "失败的WID")
    
    # 重试完全缺失的WID
    if missing_wids:
        await retry_specific_wids(missing_wids, "缺失的WID")
    
    # 重试标记为无效但可能有效的WID
    if retry_invalid_wids:
        print(f"\n❓ 发现 {len(retry_invalid_wids)} 个标记为'无效'的WID需要重新验证")
        user_input = input("是否要重试这些WID？(y/n): ").strip().lower()
        
        if user_input == 'y':
            await retry_specific_wids(retry_invalid_wids, "标记为无效的WID")
        else:
            print("⏭️ 跳过重试无效WID")
    
    # 最终统计
    print(f"\n🎯 重新下载完成!")
    print(f"请查看 flame_wid_downloads/raw_files 目录获取所有下载的文件")


if __name__ == "__main__":
    asyncio.run(main()) 