#!/usr/bin/env python3
"""
FLAME WID Database to PICS Format Converter

This script converts FLAME_WID_Complete_Database.xlsx to match the exact format of PICS.xlsx.

Target PICS format:
- Row 0: Molecular mass (rounded to nearest integer)
- Row 1: Molecular formula and compound name
- Row 2: Ionization energy and source/reference
- Row 3: Headers ("Energy(eV)" and cross-section column names)
- Row 4+: Data values (energy and photoionization cross-sections)
- Blocks separated by empty columns
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, List, Tuple, Optional

# Atomic weights for molecular mass calculation
ATOMIC_WEIGHTS = {
    'H': 1.008, 'He': 4.003, 'Li': 6.941, 'Be': 9.012, 'B': 10.811, 'C': 12.011,
    'N': 14.007, 'O': 15.999, 'F': 18.998, 'Ne': 20.180, 'Na': 22.990, 'Mg': 24.305,
    'Al': 26.982, 'Si': 28.086, 'P': 30.974, 'S': 32.065, 'Cl': 35.453, 'Ar': 39.948,
    'K': 39.098, 'Ca': 40.078, 'Sc': 44.956, 'Ti': 47.867, 'V': 50.942, 'Cr': 51.996,
    'Mn': 54.938, 'Fe': 55.845, 'Co': 58.933, 'Ni': 58.693, 'Cu': 63.546, 'Zn': 65.38,
    'Ga': 69.723, 'Ge': 72.64, 'As': 74.922, 'Se': 78.96, 'Br': 79.904, 'Kr': 83.798,
    'Rb': 85.468, 'Sr': 87.62, 'Y': 88.906, 'Zr': 91.224, 'Nb': 92.906, 'Mo': 95.96,
    'Tc': 98.0, 'Ru': 101.07, 'Rh': 102.906, 'Pd': 106.42, 'Ag': 107.868, 'Cd': 112.411,
    'In': 114.818, 'Sn': 118.71, 'Sb': 121.76, 'Te': 127.6, 'I': 126.904, 'Xe': 131.293
}

def calculate_molecular_mass(formula: str) -> float:
    """
    Calculate molecular mass from molecular formula.
    
    Args:
        formula: Molecular formula string (e.g., 'H2O', 'C6H6')
        
    Returns:
        Molecular mass in atomic mass units
    """
    if not formula or pd.isna(formula):
        return 0.0
    
    # Clean the formula string
    formula = str(formula).strip()
    
    # Handle special cases
    if formula == 'H':
        return ATOMIC_WEIGHTS['H']
    elif formula == 'He':
        return ATOMIC_WEIGHTS['He']
    
    # Parse the formula using regex
    pattern = r'([A-Z][a-z]?)(\d*)'
    matches = re.findall(pattern, formula)
    
    total_mass = 0.0
    for element, count in matches:
        if element in ATOMIC_WEIGHTS:
            count = int(count) if count else 1
            total_mass += ATOMIC_WEIGHTS[element] * count
        else:
            print(f"Warning: Unknown element '{element}' in formula '{formula}'")
    
    return total_mass

def identify_compound_blocks(df: pd.DataFrame) -> List[Tuple[int, int, str]]:
    """
    Identify compound blocks in the FLAME database.
    
    Args:
        df: DataFrame with FLAME database
        
    Returns:
        List of tuples (start_col, end_col, formula)
    """
    columns = df.columns.tolist()
    compound_blocks = []
    
    i = 0
    while i < len(columns):
        col_name = columns[i]
        
        # Skip unnamed columns
        if pd.isna(col_name) or str(col_name).startswith('Unnamed'):
            i += 1
            continue
        
        # Found a potential compound
        start_col = i
        formula = str(col_name)
        
        # Find the end of this compound block
        i += 1
        while i < len(columns) and (pd.isna(columns[i]) or str(columns[i]).startswith('Unnamed')):
            i += 1
        
        end_col = i - 1
        compound_blocks.append((start_col, end_col, formula))
    
    return compound_blocks

def extract_compound_data(df: pd.DataFrame, start_col: int, end_col: int, formula: str) -> Dict:
    """
    Extract data for a single compound.
    
    Args:
        df: DataFrame with FLAME database
        start_col: Starting column index
        end_col: Ending column index
        formula: Molecular formula
        
    Returns:
        Dictionary with compound data
    """
    # Extract basic information
    compound_name = df.iloc[0, start_col] if not pd.isna(df.iloc[0, start_col]) else ""
    reference = df.iloc[1, start_col] if not pd.isna(df.iloc[1, start_col]) else ""
    ionization_energy = df.iloc[2, start_col] if not pd.isna(df.iloc[2, start_col]) else ""
    
    # Calculate molecular mass
    molecular_mass = round(calculate_molecular_mass(formula))
    
    # Extract headers and data
    headers = []
    for col in range(start_col, end_col + 1):
        header = df.iloc[4, col]
        if not pd.isna(header):
            headers.append(str(header))
        else:
            headers.append("")
    
    # Extract data rows (from row 5 onwards)
    data_rows = []
    for row_idx in range(5, len(df)):
        row_data = []
        for col in range(start_col, end_col + 1):
            val = df.iloc[row_idx, col]
            row_data.append(val if not pd.isna(val) else "")
        
        # Check if row has any data
        if any(str(val).strip() for val in row_data if val != ""):
            data_rows.append(row_data)
        else:
            # Stop when we hit empty rows
            break
    
    return {
        'formula': formula,
        'compound_name': compound_name,
        'reference': reference,
        'ionization_energy': ionization_energy,
        'molecular_mass': molecular_mass,
        'headers': headers,
        'data_rows': data_rows,
        'num_columns': end_col - start_col + 1
    }

def convert_flame_to_pics(input_file: str, output_file: str):
    """
    Convert FLAME database to PICS format.
    
    Args:
        input_file: Path to FLAME_WID_Complete_Database.xlsx
        output_file: Path for output PICS format file
    """
    print("Reading FLAME database...")
    df = pd.read_excel(input_file, header=0)
    print(f"Loaded database with shape: {df.shape}")
    
    # Identify compound blocks
    print("Identifying compound blocks...")
    compound_blocks = identify_compound_blocks(df)
    print(f"Found {len(compound_blocks)} compounds")
    
    # Extract data for each compound
    print("Extracting compound data...")
    compounds_data = []
    for start_col, end_col, formula in compound_blocks:
        try:
            compound_data = extract_compound_data(df, start_col, end_col, formula)
            compounds_data.append(compound_data)
            print(f"Processed: {formula} ({compound_data['compound_name'][:30]}...)")
        except Exception as e:
            print(f"Error processing compound {formula}: {e}")
    
    print(f"Successfully extracted data for {len(compounds_data)} compounds")
    
    # Create PICS format DataFrame
    print("Converting to PICS format...")
    pics_data = create_pics_format(compounds_data)
    
    # Save to Excel
    print(f"Saving to {output_file}...")
    pics_data.to_excel(output_file, index=False, header=False)
    print("Conversion completed successfully!")

def create_pics_format(compounds_data: List[Dict]) -> pd.DataFrame:
    """
    Create PICS format DataFrame from compound data.
    
    Args:
        compounds_data: List of compound data dictionaries
        
    Returns:
        DataFrame in PICS format
    """
    # Calculate total columns needed
    total_cols = 0
    for compound in compounds_data:
        total_cols += compound['num_columns'] + 1  # +1 for separator column
    
    # Remove the last separator
    if total_cols > 0:
        total_cols -= 1
    
    # Calculate maximum rows needed
    max_rows = 4  # Header rows
    for compound in compounds_data:
        max_rows = max(max_rows, 4 + len(compound['data_rows']))
    
    # Create empty DataFrame
    pics_df = pd.DataFrame(index=range(max_rows), columns=range(total_cols))
    pics_df = pics_df.fillna("")
    
    # Fill in compound data
    current_col = 0
    for i, compound in enumerate(compounds_data):
        # Row 0: Molecular mass
        pics_df.iloc[0, current_col] = compound['molecular_mass']
        
        # Row 1: Formula and compound name
        pics_df.iloc[1, current_col] = compound['formula']
        if compound['compound_name'] and current_col + 1 < total_cols:
            pics_df.iloc[1, current_col + 1] = compound['compound_name']
        
        # Row 2: Ionization energy and reference
        pics_df.iloc[2, current_col] = compound['ionization_energy']
        if compound['reference'] and current_col + 1 < total_cols:
            pics_df.iloc[2, current_col + 1] = compound['reference']
        
        # Row 3: Headers
        for j, header in enumerate(compound['headers']):
            if current_col + j < total_cols and header:
                pics_df.iloc[3, current_col + j] = header
        
        # Data rows
        for row_idx, row_data in enumerate(compound['data_rows']):
            for col_idx, value in enumerate(row_data):
                if current_col + col_idx < total_cols and value != "":
                    pics_df.iloc[4 + row_idx, current_col + col_idx] = value
        
        # Move to next compound (add separator column except for last compound)
        current_col += compound['num_columns']
        if i < len(compounds_data) - 1:
            current_col += 1  # Add separator column
    
    return pics_df

if __name__ == "__main__":
    input_file = "FLAME_WID_Complete_Database.xlsx"
    output_file = "FLAME_to_PICS_converted.xlsx"
    
    try:
        convert_flame_to_pics(input_file, output_file)
    except Exception as e:
        print(f"Error during conversion: {e}")
        import traceback
        traceback.print_exc()
